CREATE TABLE IF NOT EXISTS public.suggestions
(
    suggestion_id integer NOT NULL DEFAULT nextval('suggestions_suggestion_id_seq'::regclass),
    message_id bigint NOT NULL,
    user_id bigint NOT NULL,
    guild_id bigint NOT NULL,
    content text COLLATE pg_catalog."default" NOT NULL,
    status suggestion_status DEFAULT 'pending',
    created_at bigint NOT NULL,
    updated_at bigint,
    CONSTRAINT suggestions_pkey PRIMARY KEY (suggestion_id),
    CONSTRAINT suggestions_message_id_unique UNIQUE (message_id)
);

-- Create sequence for suggestion_id if it doesn't exist
CREATE SEQUENCE IF NOT EXISTS suggestions_suggestion_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

-- Set the sequence owner
ALTER SEQUENCE suggestions_suggestion_id_seq OWNED BY suggestions.suggestion_id;

-- Enum for suggestion status
CREATE TYPE IF NOT EXISTS suggestion_status AS ENUM (
    'pending',
    'approved',
    'denied',
    'considered'
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_suggestions_guild_id ON suggestions (guild_id);
CREATE INDEX IF NOT EXISTS idx_suggestions_user_id ON suggestions (user_id);
CREATE INDEX IF NOT EXISTS idx_suggestions_status ON suggestions (status);
CREATE INDEX IF NOT EXISTS idx_suggestions_created_at ON suggestions (created_at DESC);
